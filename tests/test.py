import math
from itertools import combinations

# 定义村民好感度数据
villagers = {
    "朱敬儒": 3824,
    "货郎": 7000,
    "春香": 3775,
    "郎见秋": 7000,
    "郎永安": 7000,
    "许大爷": 7000,
    "公孙不胜": 7000,
    "马大卫": 4167,
    "关六菲": 4035,
    "金燕歌": 3835,
    "虎子": 7000,
    "阿飞": 3389,
    "小海棠": 3986,
    "王婆": 4415,
    "张得本": 3921,
    "谢小伍": 3950,
    "小渔": 3740,
    "花满山": 3703,
    "玉瑾": 3832,
    "小白": 417,
    "无名1": 0,
    "无名2": 0
}

# 游戏设定
MAX_FAVORABILITY = 7000  # 满级好感度
TARGET_FAVORABILITY = 3500  # 目标好感度


def calculate_resources(current, target):
    """计算从current到target需要的资源"""
    if current >= target:
        return 0, 0, 0, 0  # 需要好感度, 菜品数, 白菜数, 时间

    needed = target - current
    dishes_needed = math.ceil(needed / 3)
    cabbage_needed = math.ceil(dishes_needed * 1.5)  # 每个菜品需要1.5个白菜
    time_needed = cabbage_needed / (144 / 21)  # 每分钟6.857个白菜

    return needed, dishes_needed, cabbage_needed, time_needed


# 计算统计量
print("=== 村民好感度提升计算 (优化方案) ===\n")

# 游戏机制
print("📊 游戏机制:")
print("• 1个蒜蓉长白菜 = 3点好感度")
print("• 3个白菜 = 2个蒜蓉长白菜")
print("• 21分钟 = 144个白菜")
print("• 目标: 12个村民到7000好感度 + 10个村民到3500好感度")
print()

# 获取所有未满级的村民
available_villagers = [(name, fav) for name, fav in villagers.items() if fav < MAX_FAVORABILITY]
print(f"📋 可选择的村民数量: {len(available_villagers)}人")

# 分别计算每个村民到7000和3500的成本
villager_costs = []
for name, current in available_villagers:
    cost_to_7000 = calculate_resources(current, 7000)
    cost_to_3500 = calculate_resources(current, 3500)

    villager_costs.append({
        'name': name,
        'current': current,
        'to_7000': cost_to_7000,
        'to_3500': cost_to_3500,
        'diff_cost': cost_to_7000[2] - cost_to_3500[2]  # 白菜差异
    })

# 按照"升到7000的额外成本"排序，成本低的优先升到7000
villager_costs.sort(key=lambda x: x['diff_cost'])

print("\n🎯 最优分配策略:")
print("原则: 优先选择'升到7000额外成本最低'的村民升到7000")
print()

# 分配方案
to_7000_list = villager_costs[:12]  # 前12个升到7000
to_3500_list = villager_costs[12:22]  # 接下来10个升到3500

print("👑 升到7000好感度的村民 (12人):")
print("┌─────────┬──────┬──────┬─────────┬─────────┬──────────┐")
print("│ 村民名称  │ 当前 │ 需要 │ 需要菜品 │ 需要白菜 │ 需要时间  │")
print("├─────────┼──────┼──────┼─────────┼─────────┼──────────┤")

total_7000_resources = [0, 0, 0, 0]  # 好感度, 菜品, 白菜, 时间
for v in to_7000_list:
    resources = v['to_7000']
    total_7000_resources = [sum(x) for x in zip(total_7000_resources, resources)]
    time_str = f"{resources[3]:.1f}分钟"
    print(
        f"│ {v['name']:<7} │ {v['current']:>4} │ {resources[0]:>4} │ {resources[1]:>7} │ {resources[2]:>7} │ {time_str:>8} │")

print("└─────────┴──────┴──────┴─────────┴─────────┴──────────┘")

print(
    f"\n小计: 需要{total_7000_resources[0]:,}好感度, {total_7000_resources[1]:,}菜品, {total_7000_resources[2]:,}白菜, {total_7000_resources[3]:.1f}分钟")

print("\n🎖️ 升到3500好感度的村民 (10人):")
print("┌─────────┬──────┬──────┬─────────┬─────────┬──────────┐")
print("│ 村民名称  │ 当前 │ 需要 │ 需要菜品 │ 需要白菜 │ 需要时间  │")
print("├─────────┼──────┼──────┼─────────┼─────────┼──────────┤")

total_3500_resources = [0, 0, 0, 0]
for v in to_3500_list:
    resources = v['to_3500']
    total_3500_resources = [sum(x) for x in zip(total_3500_resources, resources)]
    time_str = f"{resources[3]:.1f}分钟"
    print(
        f"│ {v['name']:<7} │ {v['current']:>4} │ {resources[0]:>4} │ {resources[1]:>7} │ {resources[2]:>7} │ {time_str:>8} │")

print("└─────────┴──────┴──────┴─────────┴─────────┴──────────┘")

print(
    f"\n小计: 需要{total_3500_resources[0]:,}好感度, {total_3500_resources[1]:,}菜品, {total_3500_resources[2]:,}白菜, {total_3500_resources[3]:.1f}分钟")

# 总计算
total_resources = [sum(x) for x in zip(total_7000_resources, total_3500_resources)]
total_cycles = math.ceil(total_resources[3] / 21)

print("\n📊 总体统计 (最优方案):")
print(f"• 升到7000的村民: 12人")
print(f"• 升到3500的村民: 10人")
print(f"• 总计需要好感度: {total_resources[0]:,}点")
print(f"• 总计需要蒜蓉长白菜: {total_resources[1]:,}个")
print(f"• 总计需要白菜: {total_resources[2]:,}个")
print(f"• 总计需要时间: {total_resources[3]:.1f}分钟 ({total_resources[3] / 60:.1f}小时)")
print(f"• 需要完整周期: {total_cycles}次 (每次21分钟)")

print("\n⏰ 时间规划:")
hours = int(total_resources[3] // 60)
minutes = int(total_resources[3] % 60)
print(f"• 预计总耗时: {hours}小时{minutes}分钟")
print(f"• 如果每天游戏2小时: 需要{total_resources[3] / 120:.1f}天")
print(f"• 如果每天游戏1小时: 需要{total_resources[3] / 60:.1f}天")

print("\n💡 策略说明:")
print("• 优先选择当前好感度较高的村民升到7000 (成本效益最高)")
print("• 当前好感度较低的村民升到3500即可 (避免过度投入)")
print("• 这样可以最大化资源利用效率")

# 显示剩余村民
remaining_villagers = villager_costs[22:]
if remaining_villagers:
    print(f"\n📝 暂不处理的村民 ({len(remaining_villagers)}人):")
    for v in remaining_villagers:
        print(f"• {v['name']} (当前: {v['current']})")
