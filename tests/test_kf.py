"""
卡尔曼滤波器测试。
"""

import numpy as np
import pytest
from src.my_filters import <PERSON><PERSON><PERSON><PERSON><PERSON>


def test_kf_initialization():
    """测试卡尔曼滤波器的初始化。"""
    kf = KalmanFilter(dim_x=2, dim_z=1)
    
    assert kf.dim_x == 2
    assert kf.dim_z == 1
    assert kf.x.shape == (2, 1)
    assert kf.P.shape == (2, 2)
    assert kf.F.shape == (2, 2)
    assert kf.H.shape == (1, 2)
    assert kf.R.shape == (1, 1)
    assert kf.Q.shape == (2, 2)
    assert kf.B is None
    assert kf.u is None
    assert not kf.initialized


def test_kf_predict():
    """测试卡尔曼滤波器的预测步骤。"""
    kf = KalmanFilter(dim_x=2, dim_z=1)
    
    # 设置初始状态和参数
    kf.x = np.array([[10.0], [4.5]])  # 位置和速度
    kf.F = np.array([[1.0, 1.0], [0.0, 1.0]])  # 匀速运动模型
    kf.Q = np.array([[0.01, 0.01], [0.01, 0.01]])  # 过程噪声
    
    # 预测
    kf.predict()
    
    # 检查结果
    assert kf.initialized
    assert kf.x[0, 0] == pytest.approx(14.5)  # 新位置 = 旧位置 + 速度
    assert kf.x[1, 0] == pytest.approx(4.5)   # 速度不变


def test_kf_predict_with_control():
    """测试带控制输入的卡尔曼滤波器预测。"""
    kf = KalmanFilter(dim_x=2, dim_z=1)
    
    # 设置初始状态和参数
    kf.x = np.array([[10.0], [4.5]])  # 位置和速度
    kf.F = np.array([[1.0, 1.0], [0.0, 1.0]])  # 匀速运动模型
    kf.B = np.array([[0.0], [1.0]])  # 控制矩阵，加速度影响速度
    
    # 预测，带控制输入（加速度）
    kf.predict(u=np.array([[0.5]]))  # 加速度0.5
    
    # 检查结果
    assert kf.x[0, 0] == pytest.approx(14.5)  # 新位置 = 旧位置 + 速度
    assert kf.x[1, 0] == pytest.approx(5.0)   # 新速度 = 旧速度 + 加速度


def test_kf_update():
    """测试卡尔曼滤波器的更新步骤。"""
    kf = KalmanFilter(dim_x=2, dim_z=1)
    
    # 设置初始状态和参数
    kf.x = np.array([[10.0], [4.5]])  # 位置和速度
    kf.P = np.array([[1.0, 0.0], [0.0, 1.0]])  # 状态协方差
    kf.F = np.array([[1.0, 1.0], [0.0, 1.0]])  # 匀速运动模型
    kf.H = np.array([[1.0, 0.0]])  # 只测量位置
    kf.R = np.array([[0.1]])  # 测量噪声
    
    # 预测
    kf.predict()
    
    # 更新，测量值为15.0
    kf.update(15.0)
    
    # 检查结果（由于卡尔曼增益的计算，实际值会介于预测值和测量值之间）
    assert kf.x[0, 0] > 14.5  # 大于预测值
    assert kf.x[0, 0] < 15.0  # 小于测量值


def test_kf_full_cycle():
    """测试卡尔曼滤波器的完整周期（预测+更新）。"""
    kf = KalmanFilter(dim_x=2, dim_z=1)
    
    # 设置初始状态和参数
    kf.x = np.array([[0.0], [1.0]])  # 位置和速度
    kf.P = np.array([[1.0, 0.0], [0.0, 1.0]])  # 状态协方差
    kf.F = np.array([[1.0, 1.0], [0.0, 1.0]])  # 匀速运动模型
    kf.H = np.array([[1.0, 0.0]])  # 只测量位置
    kf.R = np.array([[0.1]])  # 测量噪声
    kf.Q = np.array([[0.01, 0.01], [0.01, 0.01]])  # 过程噪声
    
    # 创建测量值
    measurements = [1.1, 2.0, 3.2, 4.0, 5.1]
    
    # 运行滤波器
    filtered_states = []
    for z in measurements:
        kf.predict()
        kf.update(z)
        filtered_states.append(kf.x.copy())
    
    # 检查结果
    assert len(filtered_states) == len(measurements)
    
    # 检查最终状态（位置应该接近最后的测量值）
    assert filtered_states[-1][0, 0] == pytest.approx(5.1, abs=0.5)


if __name__ == "__main__":
    pytest.main(["-xvs", __file__]) 