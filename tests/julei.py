import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from scipy.cluster.hierarchy import dendrogram, linkage, fcluster
from sklearn.cluster import AgglomerativeClustering
from sklearn.preprocessing import StandardScaler
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 1. 准备数据
cities = ['北京', '上海', '广州', '深圳', '哈尔滨', '昆明']
temperature = [12, 16, 22, 23, 2, 15]
humidity = [45, 65, 75, 78, 35, 55]

# 创建数据框
data = pd.DataFrame({
    '城市': cities,
    '温度': temperature,
    '湿度': humidity
})

print("原始数据：")
print(data)
print()

# 2. 数据标准化（重要！）
features = data[['温度', '湿度']].values
scaler = StandardScaler()
features_scaled = scaler.fit_transform(features)

print("标准化后的数据：")
scaled_df = pd.DataFrame(features_scaled, columns=['温度_标准化', '湿度_标准化'])
scaled_df['城市'] = cities
print(scaled_df)
print()

# 3. 使用scipy进行层次聚类
# 计算距离矩阵并进行聚类
linkage_matrix = linkage(features_scaled, method='ward')

# 4. 绘制树状图
plt.figure(figsize=(12, 8))
plt.subplot(2, 2, 1)
dendrogram(linkage_matrix, labels=cities, orientation='top')
plt.title('层次聚类树状图')
plt.xlabel('城市')
plt.ylabel('距离')

# 5. 使用sklearn进行层次聚类
# 指定聚类数量
n_clusters = 3
hierarchical = AgglomerativeClustering(n_clusters=n_clusters, linkage='ward')
cluster_labels = hierarchical.fit_predict(features_scaled)

# 将聚类结果添加到数据框
data['聚类'] = cluster_labels

print("聚类结果：")
for i in range(n_clusters):
    cluster_cities = data[data['聚类'] == i]['城市'].tolist()
    print(f"簇 {i}: {cluster_cities}")
print()

# 6. 可视化聚类结果
plt.subplot(2, 2, 2)
colors = ['red', 'blue', 'green', 'purple', 'orange']
for i in range(n_clusters):
    cluster_data = data[data['聚类'] == i]
    plt.scatter(cluster_data['温度'], cluster_data['湿度'],
                c=colors[i], label=f'簇 {i}', s=100)

    # 添加城市标签
    for idx, row in cluster_data.iterrows():
        plt.annotate(row['城市'], (row['温度'], row['湿度']),
                     xytext=(5, 5), textcoords='offset points')

plt.xlabel('温度 (°C)')
plt.ylabel('湿度 (%)')
plt.title('聚类结果可视化')
plt.legend()
plt.grid(True, alpha=0.3)

# 7. 不同聚类数量的比较
plt.subplot(2, 2, 3)
cluster_range = range(2, 6)
inertias = []

for n in cluster_range:
    hierarchical = AgglomerativeClustering(n_clusters=n, linkage='ward')
    labels = hierarchical.fit_predict(features_scaled)

    # 计算簇内平方和
    inertia = 0
    for i in range(n):
        cluster_points = features_scaled[labels == i]
        if len(cluster_points) > 0:
            centroid = np.mean(cluster_points, axis=0)
            inertia += np.sum((cluster_points - centroid) ** 2)
    inertias.append(inertia)

plt.plot(cluster_range, inertias, 'bo-')
plt.xlabel('聚类数量')
plt.ylabel('簇内平方和')
plt.title('肘部法则')
plt.grid(True, alpha=0.3)

# 8. 距离矩阵热力图
plt.subplot(2, 2, 4)
from scipy.spatial.distance import pdist, squareform

distance_matrix = squareform(pdist(features_scaled))
sns.heatmap(distance_matrix, annot=True, fmt='.2f',
            xticklabels=cities, yticklabels=cities, cmap='viridis')
plt.title('城市间距离矩阵')

plt.tight_layout()
plt.show()

# 9. 详细分析不同linkage方法
methods = ['ward', 'complete', 'average', 'single']
plt.figure(figsize=(15, 10))

for i, method in enumerate(methods):
    plt.subplot(2, 2, i + 1)
    linkage_matrix = linkage(features_scaled, method=method)
    dendrogram(linkage_matrix, labels=cities, orientation='top')
    plt.title(f'{method.capitalize()} Linkage')
    plt.xlabel('城市')
    plt.ylabel('距离')

plt.tight_layout()
plt.show()

# 10. 使用不同距离度量
print("不同聚类方法的比较：")
print("-" * 50)

methods = ['ward', 'complete', 'average', 'single']
for method in methods:
    hierarchical = AgglomerativeClustering(n_clusters=3, linkage=method)
    labels = hierarchical.fit_predict(features_scaled)

    print(f"\n{method.capitalize()} Linkage 结果:")
    for i in range(3):
        cluster_cities = [cities[j] for j in range(len(cities)) if labels[j] == i]
        print(f"  簇 {i}: {cluster_cities}")
