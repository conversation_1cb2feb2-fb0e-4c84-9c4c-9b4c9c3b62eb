# My Filters

一个Python滤波器库，提供各种常用的滤波算法实现，包括卡尔曼滤波器(KF)、扩展卡尔曼滤波器(EKF)、无迹卡尔曼滤波器(UKF)和粒子滤波器(PF)等。

## 特性

- 模块化设计，易于扩展
- 统一的API接口
- 丰富的示例和文档
- 完整的测试覆盖

## 安装

```bash
pip install my_filters
```

或者从源码安装：

```bash
git clone https://github.com/yourusername/my_filters.git
cd my_filters
pip install -e .
```

## 快速开始

### 卡尔曼滤波器示例

```python
import numpy as np
from my_filters import KalmanFilter

# 创建滤波器
kf = KalmanFilter(
    dim_x=2,  # 状态维度
    dim_z=1,  # 测量维度
)

# 设置初始状态
kf.x = np.array([0., 0.])  # 位置和速度

# 设置状态转移矩阵
kf.F = np.array([[1., 1.], [0., 1.]])  # 匀速运动模型

# 设置测量矩阵
kf.H = np.array([[1., 0.]])  # 只测量位置

# 设置过程噪声协方差
kf.Q = np.eye(2) * 0.01

# 设置测量噪声协方差
kf.R = np.array([[0.1]])

# 运行滤波器
measurements = [0.1, 0.2, 0.3, 0.4, 0.5]
filtered_states = []

for z in measurements:
    kf.predict()
    kf.update(z)
    filtered_states.append(kf.x.copy())

print(filtered_states)
```

## 文档

详细文档请参阅[docs](docs/)目录。

## 支持的滤波器

- 卡尔曼滤波器 (KF)
- 扩展卡尔曼滤波器 (EKF)
- 无迹卡尔曼滤波器 (UKF)
- 粒子滤波器 (PF)

## 贡献

欢迎贡献代码、报告问题或提出改进建议。请参阅[贡献指南](CONTRIBUTING.md)。

## 许可证

本项目采用MIT许可证 - 详见[LICENSE](LICENSE)文件。 