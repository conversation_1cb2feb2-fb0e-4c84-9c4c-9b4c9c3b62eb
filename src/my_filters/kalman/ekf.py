"""
扩展卡尔曼滤波器实现。
"""

import numpy as np
from ..base import Filter
from ..common.math_utils import ensure_matrix


class ExtendedKalmanFilter(Filter):
    """
    扩展卡尔曼滤波器实现。
    
    该滤波器适用于非线性系统，通过一阶泰勒展开进行线性化。
    """
    
    def __init__(self, dim_x, dim_z):
        """
        初始化扩展卡尔曼滤波器。
        
        参数:
            dim_x (int): 状态向量的维度
            dim_z (int): 测量向量的维度
        """
        super().__init__(dim_x, dim_z)
        
        # 非线性状态转移函数
        self.f = None
        
        # 非线性测量函数
        self.h = None
        
        # 状态转移雅可比矩阵函数
        self.F_jacobian = None
        
        # 测量雅可比矩阵函数
        self.H_jacobian = None
        
        # 控制输入
        self.u = None
    
    def predict(self, u=None):
        """
        预测步骤: 使用非线性状态转移函数预测下一状态。
        
        参数:
            u (可选): 控制输入向量
        """
        if u is not None:
            self.u = u
        
        if self.f is None:
            raise ValueError("必须设置状态转移函数 f")
        
        if self.F_jacobian is None:
            raise ValueError("必须设置状态转移雅可比矩阵函数 F_jacobian")
        
        # 使用非线性函数预测状态
        if self.u is not None:
            self.x = self.f(self.x, self.u)
        else:
            self.x = self.f(self.x)
        
        # 计算雅可比矩阵
        if self.u is not None:
            F = self.F_jacobian(self.x, self.u)
        else:
            F = self.F_jacobian(self.x)
        
        # 预测协方差
        self.P = F @ self.P @ F.T + self.Q
        
        # 标记为已初始化
        self.initialized = True
    
    def update(self, z):
        """
        更新步骤: 使用非线性测量函数和测量值更新状态估计。
        
        参数:
            z: 测量值，可以是标量或向量
        """
        z = ensure_matrix(z)
        
        if self.h is None:
            raise ValueError("必须设置测量函数 h")
        
        if self.H_jacobian is None:
            raise ValueError("必须设置测量雅可比矩阵函数 H_jacobian")
        
        # 如果滤波器未初始化，则使用第一次测量进行初始化
        if not self.initialized:
            # 简单初始化，实际应用中可能需要更复杂的初始化方法
            self.x[:self.dim_z] = z
            self.initialized = True
            return
        
        # 计算预测的测量值
        h_x = self.h(self.x)
        
        # 计算测量雅可比矩阵
        H = self.H_jacobian(self.x)
        
        # 计算测量残差
        y = z - h_x
        
        # 计算残差协方差
        S = H @ self.P @ H.T + self.R
        
        # 计算卡尔曼增益
        K = self.P @ H.T @ np.linalg.inv(S)
        self.K = K
        
        # 更新状态估计
        self.x = self.x + K @ y
        
        # 更新状态协方差
        I = np.eye(self.dim_x)
        self.P = (I - K @ H) @ self.P
    
    def set_functions(self, f, h, F_jacobian, H_jacobian):
        """
        设置非线性函数和雅可比矩阵函数。
        
        参数:
            f: 非线性状态转移函数
            h: 非线性测量函数
            F_jacobian: 状态转移雅可比矩阵函数
            H_jacobian: 测量雅可比矩阵函数
        """
        self.f = f
        self.h = h
        self.F_jacobian = F_jacobian
        self.H_jacobian = H_jacobian 