"""
标准卡尔曼滤波器实现。
"""

import numpy as np
from ..base import Filter
from ..common.math_utils import ensure_matrix


class KalmanFilter(Filter):
    """
    标准卡尔曼滤波器实现。
    
    该滤波器适用于线性系统，状态转移和测量都是线性的。
    """
    
    def __init__(self, dim_x, dim_z):
        """
        初始化卡尔曼滤波器。
        
        参数:
            dim_x (int): 状态向量的维度
            dim_z (int): 测量向量的维度
        """
        super().__init__(dim_x, dim_z)
        
        # 控制矩阵
        self.B = None
        
        # 控制向量
        self.u = None
    
    def predict(self, u=None):
        """
        预测步骤: 根据系统模型预测下一状态。
        
        参数:
            u (可选): 控制输入向量
        """
        # 保存控制输入
        if u is not None:
            self.u = ensure_matrix(u)
        
        # 状态预测
        if self.u is not None and self.B is not None:
            self.x = self.F @ self.x + self.B @ self.u
        else:
            self.x = self.F @ self.x
        
        # 协方差预测
        self.P = self.F @ self.P @ self.F.T + self.Q
        
        # 标记为已初始化
        self.initialized = True
    
    def update(self, z):
        """
        更新步骤: 根据测量值更新状态估计。
        
        参数:
            z: 测量值，可以是标量或向量
        """
        # 确保z是矩阵形式
        z = ensure_matrix(z)
        
        # 如果滤波器未初始化，则只使用第一次测量进行初始化
        if not self.initialized:
            self.x = self.H.T @ np.linalg.inv(self.H @ self.H.T) @ z
            self.initialized = True
            return
        
        # 计算测量残差
        y = z - self.H @ self.x
        
        # 计算残差协方差
        S = self.H @ self.P @ self.H.T + self.R
        
        # 计算卡尔曼增益
        K = self.P @ self.H.T @ np.linalg.inv(S)
        self.K = K
        
        # 更新状态估计
        self.x = self.x + K @ y
        
        # 更新状态协方差
        I = np.eye(self.dim_x)
        self.P = (I - K @ self.H) @ self.P
    
    def set_control_matrix(self, B):
        """
        设置控制矩阵。
        
        参数:
            B: 控制矩阵
        """
        self.B = ensure_matrix(B)
    
    def get_log_likelihood(self, z):
        """
        计算给定测量值的对数似然。
        
        参数:
            z: 测量值
            
        返回:
            对数似然值
        """
        z = ensure_matrix(z)
        
        # 计算测量残差
        y = z - self.H @ self.x
        
        # 计算残差协方差
        S = self.H @ self.P @ self.H.T + self.R
        
        # 计算对数似然
        n = len(z)
        log_likelihood = -0.5 * (y.T @ np.linalg.inv(S) @ y + np.log(np.linalg.det(S)) + n * np.log(2 * np.pi))
        
        return float(log_likelihood) 