"""
无迹卡尔曼滤波器实现。
"""

import numpy as np
from ..base import Filter
from ..common.math_utils import ensure_matrix, unscented_transform


class UnscentedKalmanFilter(Filter):
    """
    无迹卡尔曼滤波器实现。
    
    该滤波器使用sigma点来处理非线性系统，无需计算雅可比矩阵。
    相比于EKF，UKF通常能提供更好的非线性处理能力。
    """
    
    def __init__(self, dim_x, dim_z, alpha=0.1, beta=2.0, kappa=0.0):
        """
        初始化无迹卡尔曼滤波器。
        
        参数:
            dim_x (int): 状态向量的维度
            dim_z (int): 测量向量的维度
            alpha (float): 缩放参数，控制sigma点的分布，通常为1e-3到1之间的小正数
            beta (float): 先验分布参数，高斯分布的最优值为2
            kappa (float): 次要缩放参数，通常为0
        """
        super().__init__(dim_x, dim_z)
        
        # UKF特定参数
        self.alpha = alpha
        self.beta = beta
        self.kappa = kappa
        
        # 计算缩放因子
        self.lambda_ = self.alpha**2 * (self.dim_x + self.kappa) - self.dim_x
        
        # 计算权重
        self._compute_weights()
        
        # 状态传播函数和测量函数
        self.f = None  # 状态传播函数
        self.h = None  # 测量函数
        
        # sigma点
        self.sigma_points = None
        
        # 控制输入
        self.u = None
    
    def _compute_weights(self):
        """
        计算sigma点的权重。
        """
        n = self.dim_x
        lambda_ = self.lambda_
        
        # 创建权重数组
        self.Wm = np.zeros(2 * n + 1)  # 均值权重
        self.Wc = np.zeros(2 * n + 1)  # 协方差权重
        
        # 计算权重
        self.Wm[0] = lambda_ / (n + lambda_)
        self.Wc[0] = self.Wm[0] + (1 - self.alpha**2 + self.beta)
        
        for i in range(1, 2 * n + 1):
            self.Wm[i] = 1.0 / (2 * (n + lambda_))
            self.Wc[i] = self.Wm[i]
    
    def _generate_sigma_points(self):
        """
        生成sigma点。
        
        返回:
            sigma_points: 生成的sigma点数组，形状为(2*dim_x+1, dim_x)
        """
        n = self.dim_x
        lambda_ = self.lambda_
        
        # 计算矩阵平方根
        U = np.linalg.cholesky((n + lambda_) * self.P)
        
        # 创建sigma点数组
        sigma_points = np.zeros((2 * n + 1, n))
        sigma_points[0] = self.x.flatten()
        
        for i in range(n):
            sigma_points[i + 1] = self.x.flatten() + U[i]
            sigma_points[n + i + 1] = self.x.flatten() - U[i]
        
        self.sigma_points = sigma_points
        return sigma_points
    
    def predict(self, u=None):
        """
        预测步骤: 使用sigma点和非线性状态传播函数预测下一状态。
        
        参数:
            u (可选): 控制输入向量
        """
        if u is not None:
            self.u = u
        
        if self.f is None:
            raise ValueError("必须设置状态传播函数 f")
        
        # 生成sigma点
        sigma_points = self._generate_sigma_points()
        n_sigmas = sigma_points.shape[0]
        
        # 通过非线性状态传播函数传播sigma点
        transformed_sigmas = np.zeros_like(sigma_points)
        for i in range(n_sigmas):
            if self.u is not None:
                transformed_sigmas[i] = self.f(sigma_points[i], self.u)
            else:
                transformed_sigmas[i] = self.f(sigma_points[i])
        
        # 使用无迹变换计算预测后的均值和协方差
        self.x, self.P = unscented_transform(transformed_sigmas, self.Wm, self.Q)
        self.x = self.x.reshape(-1, 1)
        
        # 保存变换后的sigma点
        self.sigma_points = transformed_sigmas
        
        # 标记为已初始化
        self.initialized = True
    
    def update(self, z):
        """
        更新步骤: 使用sigma点和非线性测量函数更新状态估计。
        
        参数:
            z: 测量值，可以是标量或向量
        """
        z = ensure_matrix(z)
        
        if self.h is None:
            raise ValueError("必须设置测量函数 h")
        
        # 如果滤波器未初始化，则使用第一次测量进行初始化
        if not self.initialized:
            # 简单初始化，实际应用中可能需要更复杂的初始化方法
            self.x[:self.dim_z] = z
            self.initialized = True
            return
        
        n_sigmas = self.sigma_points.shape[0]
        
        # 通过测量函数转换sigma点
        transformed_sigmas = np.zeros((n_sigmas, self.dim_z))
        for i in range(n_sigmas):
            transformed_sigmas[i] = self.h(self.sigma_points[i])
        
        # 使用无迹变换计算预测的测量值和协方差
        z_pred, Pz = unscented_transform(transformed_sigmas, self.Wm, self.R)
        
        # 计算状态和测量的互相关矩阵
        Pxz = np.zeros((self.dim_x, self.dim_z))
        for i in range(n_sigmas):
            dx = self.sigma_points[i] - self.x.flatten()
            dz = transformed_sigmas[i] - z_pred
            Pxz += self.Wc[i] * np.outer(dx, dz)
        
        # 计算卡尔曼增益
        K = Pxz @ np.linalg.inv(Pz)
        self.K = K
        
        # 更新状态估计
        self.x = self.x + K @ (z - z_pred.reshape(-1, 1))
        
        # 更新状态协方差
        self.P = self.P - K @ Pz @ K.T
    
    def set_functions(self, f, h):
        """
        设置状态传播函数和测量函数。
        
        参数:
            f: 状态传播函数
            h: 测量函数
        """
        self.f = f
        self.h = h 