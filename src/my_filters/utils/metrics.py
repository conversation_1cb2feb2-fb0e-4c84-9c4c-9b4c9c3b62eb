"""
滤波性能评估指标。
"""

import numpy as np


def mean_squared_error(estimated_states, true_states):
    """
    计算均方误差(MSE)。
    
    参数:
        estimated_states: 估计状态列表或数组
        true_states: 真实状态列表或数组
        
    返回:
        mse: 均方误差
    """
    n = len(estimated_states)
    if n != len(true_states):
        raise ValueError("估计状态和真实状态数量不匹配")
    
    squared_errors = []
    
    for i in range(n):
        est = estimated_states[i]
        true = true_states[i]
        
        # 确保数据格式一致
        if hasattr(est, 'shape') and len(est.shape) > 1:
            est = est.flatten()
        if hasattr(true, 'shape') and len(true.shape) > 1:
            true = true.flatten()
        
        # 计算平方误差
        squared_error = np.mean((est - true) ** 2)
        squared_errors.append(squared_error)
    
    return np.mean(squared_errors)


def root_mean_squared_error(estimated_states, true_states):
    """
    计算均方根误差(RMSE)。
    
    参数:
        estimated_states: 估计状态列表或数组
        true_states: 真实状态列表或数组
        
    返回:
        rmse: 均方根误差
    """
    return np.sqrt(mean_squared_error(estimated_states, true_states))


def mean_absolute_error(estimated_states, true_states):
    """
    计算平均绝对误差(MAE)。
    
    参数:
        estimated_states: 估计状态列表或数组
        true_states: 真实状态列表或数组
        
    返回:
        mae: 平均绝对误差
    """
    n = len(estimated_states)
    if n != len(true_states):
        raise ValueError("估计状态和真实状态数量不匹配")
    
    absolute_errors = []
    
    for i in range(n):
        est = estimated_states[i]
        true = true_states[i]
        
        # 确保数据格式一致
        if hasattr(est, 'shape') and len(est.shape) > 1:
            est = est.flatten()
        if hasattr(true, 'shape') and len(true.shape) > 1:
            true = true.flatten()
        
        # 计算绝对误差
        absolute_error = np.mean(np.abs(est - true))
        absolute_errors.append(absolute_error)
    
    return np.mean(absolute_errors)


def normalized_estimation_error_squared(estimated_states, true_states, covariances):
    """
    计算归一化估计误差平方(NEES)，用于评估滤波器的一致性。
    
    参数:
        estimated_states: 估计状态列表或数组
        true_states: 真实状态列表或数组
        covariances: 估计协方差矩阵列表或数组
        
    返回:
        nees: 归一化估计误差平方
    """
    n = len(estimated_states)
    if n != len(true_states) or n != len(covariances):
        raise ValueError("估计状态、真实状态和协方差数量不匹配")
    
    nees_values = []
    
    for i in range(n):
        est = estimated_states[i]
        true = true_states[i]
        cov = covariances[i]
        
        # 确保数据格式一致
        if hasattr(est, 'shape') and len(est.shape) > 1:
            est = est.flatten()
        if hasattr(true, 'shape') and len(true.shape) > 1:
            true = true.flatten()
        
        # 计算误差
        error = est - true
        
        # 计算NEES
        nees = error.T @ np.linalg.inv(cov) @ error
        nees_values.append(nees)
    
    return np.mean(nees_values)


def consistency_test(nees_values, dim_x, confidence=0.95):
    """
    执行滤波器一致性测试。
    
    参数:
        nees_values: NEES值列表或数组
        dim_x: 状态维度
        confidence: 置信度，默认为0.95
        
    返回:
        is_consistent: 滤波器是否一致
    """
    import scipy.stats as stats
    
    n = len(nees_values)
    
    # 计算区间上下界
    lower_bound = stats.chi2.ppf((1 - confidence) / 2, n * dim_x) / n
    upper_bound = stats.chi2.ppf((1 + confidence) / 2, n * dim_x) / n
    
    # 计算平均NEES
    avg_nees = np.mean(nees_values)
    
    # 判断一致性
    return lower_bound <= avg_nees <= upper_bound 