"""
My Filters - 一个Python滤波器库

该库提供了多种滤波算法的实现，包括:
- 卡尔曼滤波器 (KF)
- 扩展卡尔曼滤波器 (EKF)
- 无迹卡尔曼滤波器 (UKF)
- 粒子滤波器 (PF)
"""

__version__ = "0.1.0"

# 导入滤波器基类
from .base import Filter

# 导入卡尔曼滤波器系列
from .kalman import KalmanFilter, ExtendedKalmanFilter, UnscentedKalmanFilter

# 导入粒子滤波器
from .particle import ParticleFilter

# 导入评估指标
from .utils.metrics import (
    mean_squared_error, 
    root_mean_squared_error, 
    mean_absolute_error,
    normalized_estimation_error_squared, 
    consistency_test
)

# 导入可视化工具
from .common.visualization import (
    plot_filter_results,
    plot_trajectory_2d,
    confidence_ellipse
)

# 导出所有接口
__all__ = [
    # 基类
    'Filter',
    
    # 卡尔曼滤波器系列
    'KalmanFilter',
    'ExtendedKalmanFilter',
    'UnscentedKalmanFilter',
    
    # 粒子滤波器
    'ParticleFilter',
    
    # 评估指标
    'mean_squared_error',
    'root_mean_squared_error',
    'mean_absolute_error',
    'normalized_estimation_error_squared',
    'consistency_test',
    
    # 可视化工具
    'plot_filter_results',
    'plot_trajectory_2d',
    'confidence_ellipse',
]
