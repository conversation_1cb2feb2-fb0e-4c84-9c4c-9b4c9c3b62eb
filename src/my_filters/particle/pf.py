"""
粒子滤波器实现。
"""

import numpy as np
from ..base import Filter
from ..common.math_utils import ensure_matrix


class ParticleFilter(Filter):
    """
    粒子滤波器实现。
    
    该滤波器使用一组粒子（样本）来表示概率分布，适用于非线性、非高斯系统。
    """
    
    def __init__(self, dim_x, dim_z, n_particles=100):
        """
        初始化粒子滤波器。
        
        参数:
            dim_x (int): 状态向量的维度
            dim_z (int): 测量向量的维度
            n_particles (int): 粒子数量
        """
        super().__init__(dim_x, dim_z)
        
        # 粒子数量
        self.n_particles = n_particles
        
        # 粒子 (n_particles x dim_x)
        self.particles = np.zeros((n_particles, dim_x))
        
        # 粒子权重
        self.weights = np.ones(n_particles) / n_particles
        
        # 状态传播函数
        self.f = None
        
        # 测量似然函数
        self.likelihood_fn = None
        
        # 重采样阈值
        self.resample_threshold = 0.5
        
        # 过程噪声生成函数
        self.process_noise_fn = None
        
        # 控制输入
        self.u = None
    
    def initialize_particles(self, mean=None, cov=None, fn=None):
        """
        初始化粒子。
        
        参数:
            mean (可选): 初始均值
            cov (可选): 初始协方差
            fn (可选): 自定义初始化函数
        """
        if fn is not None:
            self.particles = fn(self.n_particles, self.dim_x)
        elif mean is not None and cov is not None:
            self.particles = np.random.multivariate_normal(
                mean.flatten(), cov, size=self.n_particles
            )
        else:
            # 默认初始化为标准正态分布
            self.particles = np.random.randn(self.n_particles, self.dim_x)
        
        self.weights = np.ones(self.n_particles) / self.n_particles
        self.initialized = True
    
    def predict(self, u=None):
        """
        预测步骤: 使用状态传播函数和过程噪声传播粒子。
        
        参数:
            u (可选): 控制输入向量
        """
        if u is not None:
            self.u = u
        
        if not self.initialized:
            raise ValueError("粒子滤波器尚未初始化，请调用initialize_particles")
        
        if self.f is None:
            raise ValueError("必须设置状态传播函数 f")
        
        # 传播每个粒子
        for i in range(self.n_particles):
            # 应用状态传播函数
            if self.u is not None:
                self.particles[i] = self.f(self.particles[i], self.u)
            else:
                self.particles[i] = self.f(self.particles[i])
            
            # 添加过程噪声
            if self.process_noise_fn is not None:
                self.particles[i] += self.process_noise_fn(self.dim_x)
            else:
                # 默认使用协方差矩阵的噪声
                self.particles[i] += np.random.multivariate_normal(
                    np.zeros(self.dim_x), self.Q
                )
        
        # 更新状态估计
        self._update_state_estimate()
    
    def update(self, z):
        """
        更新步骤: 使用测量值更新粒子权重。
        
        参数:
            z: 测量值，可以是标量或向量
        """
        z = ensure_matrix(z)
        
        if not self.initialized:
            raise ValueError("粒子滤波器尚未初始化，请调用initialize_particles")
        
        if self.likelihood_fn is None:
            raise ValueError("必须设置测量似然函数 likelihood_fn")
        
        # 计算每个粒子的似然，更新权重
        for i in range(self.n_particles):
            self.weights[i] *= self.likelihood_fn(z, self.particles[i])
        
        # 归一化权重
        if np.sum(self.weights) > 0:
            self.weights /= np.sum(self.weights)
        else:
            # 如果所有粒子权重都为零，重新初始化
            self.weights = np.ones(self.n_particles) / self.n_particles
            print("警告: 所有粒子权重为零，已重置")
        
        # 计算有效粒子数量
        n_eff = 1.0 / np.sum(np.square(self.weights))
        
        # 如果有效粒子数量低于阈值，则重采样
        if n_eff < self.n_particles * self.resample_threshold:
            self._resample()
        
        # 更新状态估计
        self._update_state_estimate()
    
    def _resample(self):
        """
        重采样步骤: 根据权重重新采样粒子。
        """
        # 累积权重
        cum_weights = np.cumsum(self.weights)
        
        # 生成随机数
        random_numbers = np.random.random(self.n_particles)
        random_numbers.sort()
        
        # 重采样
        new_particles = np.zeros_like(self.particles)
        i, j = 0, 0
        
        while i < self.n_particles:
            if random_numbers[i] < cum_weights[j]:
                new_particles[i] = self.particles[j]
                i += 1
            else:
                j += 1
        
        # 更新粒子和权重
        self.particles = new_particles
        self.weights = np.ones(self.n_particles) / self.n_particles
    
    def _update_state_estimate(self):
        """
        根据粒子和权重更新状态估计。
        """
        # 计算加权平均
        self.x = np.sum(self.particles.T * self.weights, axis=1).reshape(-1, 1)
        
        # 计算协方差
        dx = self.particles - self.x.T
        self.P = np.zeros((self.dim_x, self.dim_x))
        
        for i in range(self.n_particles):
            self.P += self.weights[i] * np.outer(dx[i], dx[i])
    
    def set_functions(self, f, likelihood_fn, process_noise_fn=None):
        """
        设置状态传播函数和测量似然函数。
        
        参数:
            f: 状态传播函数
            likelihood_fn: 测量似然函数，计算 p(z|x)
            process_noise_fn (可选): 过程噪声生成函数
        """
        self.f = f
        self.likelihood_fn = likelihood_fn
        self.process_noise_fn = process_noise_fn
    
    def get_particles(self):
        """
        获取当前粒子和权重。
        
        返回:
            particles: 粒子数组
            weights: 权重数组
        """
        return self.particles, self.weights 