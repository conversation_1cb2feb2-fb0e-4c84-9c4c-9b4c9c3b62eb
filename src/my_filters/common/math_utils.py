"""
数学工具函数，为滤波器实现提供支持。
"""

import numpy as np


def ensure_matrix(x):
    """
    确保输入是一个numpy矩阵。
    
    参数:
        x: 输入数据，可以是标量、列表、数组等
        
    返回:
        numpy矩阵形式的输入数据
    """
    if np.isscalar(x):
        return np.array([[x]])
    
    x = np.asarray(x)
    if x.ndim == 1:
        return x.reshape((-1, 1))
    
    return x


def normalize_angle(x):
    """
    将角度标准化到[-pi, pi]范围内。
    
    参数:
        x: 角度值（弧度）
        
    返回:
        标准化后的角度值
    """
    return (x + np.pi) % (2 * np.pi) - np.pi


def mahalanobis(x, mean, cov):
    """
    计算马氏距离。
    
    参数:
        x: 向量
        mean: 均值向量
        cov: 协方差矩阵
        
    返回:
        马氏距离值
    """
    diff = x - mean
    inv_cov = np.linalg.inv(cov)
    
    return np.sqrt(diff.T @ inv_cov @ diff)


def cholesky_update(L, x, sign=1):
    """
    Cholesky分解的秩1更新。
    
    参数:
        L: 下三角Cholesky因子
        x: 更新向量
        sign: 更新符号(+1或-1)
        
    返回:
        更新后的Cholesky因子
    """
    p = len(x)
    x = x.copy()
    
    for k in range(p):
        if sign == 1:
            r = np.sqrt(L[k, k]**2 + x[k]**2)
        else:
            r = np.sqrt(L[k, k]**2 - x[k]**2)
        
        c = r / L[k, k]
        s = x[k] / L[k, k]
        L[k, k] = r
        
        if k < p - 1:
            L[k+1:, k] = (L[k+1:, k] + sign * s * x[k+1:]) / c
            x[k+1:] = c * x[k+1:] - s * L[k+1:, k]
    
    return L


def unscented_transform(sigma_points, weights, noise_cov=None):
    """
    无迹变换，用于UKF。
    
    参数:
        sigma_points: sigma点集
        weights: 权重
        noise_cov: 噪声协方差矩阵（可选）
        
    返回:
        mean: 变换后的均值
        cov: 变换后的协方差
    """
    n = sigma_points.shape[1]
    mean = np.zeros(n)
    
    # 计算均值
    for i in range(len(weights)):
        mean += weights[i] * sigma_points[i]
    
    # 计算协方差
    cov = np.zeros((n, n))
    for i in range(len(weights)):
        diff = sigma_points[i] - mean
        cov += weights[i] * np.outer(diff, diff)
    
    # 添加噪声协方差
    if noise_cov is not None:
        cov += noise_cov
    
    return mean, cov 