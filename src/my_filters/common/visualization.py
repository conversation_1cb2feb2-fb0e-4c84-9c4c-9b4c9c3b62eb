"""
可视化工具，用于绘制滤波结果。
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Ellipse
import matplotlib.transforms as transforms


def plot_filter_results(times, measurements, filtered_states, true_states=None, labels=None, title=None):
    """
    绘制滤波结果。
    
    参数:
        times: 时间点
        measurements: 测量值
        filtered_states: 滤波后的状态估计
        true_states: 真实状态（可选）
        labels: 坐标轴标签（可选）
        title: 图表标题（可选）
    """
    plt.figure(figsize=(10, 6))
    
    # 绘制测量值
    plt.scatter(times, measurements, color='r', alpha=0.5, label='测量值')
    
    # 绘制滤波后的状态估计
    filtered_values = [state[0, 0] if hasattr(state, 'shape') and len(state.shape) > 1 else state[0] for state in filtered_states]
    plt.plot(times, filtered_values, 'b-', label='滤波估计')
    
    # 如果有真实状态，则绘制
    if true_states is not None:
        true_values = [state[0, 0] if hasattr(state, 'shape') and len(state.shape) > 1 else state[0] for state in true_states]
        plt.plot(times, true_values, 'g--', label='真实状态')
    
    plt.legend()
    
    if labels:
        plt.xlabel(labels[0])
        plt.ylabel(labels[1])
    else:
        plt.xlabel('时间')
        plt.ylabel('状态')
    
    if title:
        plt.title(title)
    else:
        plt.title('滤波结果')
    
    plt.grid(True)
    plt.tight_layout()
    
    return plt.gcf()


def plot_trajectory_2d(measurements, filtered_states, true_states=None, covariances=None, title=None):
    """
    绘制2D轨迹。
    
    参数:
        measurements: 测量值 (x, y)
        filtered_states: 滤波后的状态估计 (x, y, ...)
        true_states: 真实状态（可选）(x, y, ...)
        covariances: 状态协方差矩阵列表（可选）
        title: 图表标题（可选）
    """
    plt.figure(figsize=(10, 8))
    
    # 提取坐标
    meas_x = [m[0] for m in measurements]
    meas_y = [m[1] for m in measurements]
    
    filt_x = [s[0, 0] if hasattr(s, 'shape') and len(s.shape) > 1 else s[0] for s in filtered_states]
    filt_y = [s[1, 0] if hasattr(s, 'shape') and len(s.shape) > 1 else s[1] for s in filtered_states]
    
    # 绘制测量值
    plt.scatter(meas_x, meas_y, color='r', alpha=0.5, label='测量值')
    
    # 绘制滤波后的轨迹
    plt.plot(filt_x, filt_y, 'b-', label='滤波估计')
    
    # 如果有真实状态，则绘制
    if true_states is not None:
        true_x = [s[0, 0] if hasattr(s, 'shape') and len(s.shape) > 1 else s[0] for s in true_states]
        true_y = [s[1, 0] if hasattr(s, 'shape') and len(s.shape) > 1 else s[1] for s in true_states]
        plt.plot(true_x, true_y, 'g--', label='真实轨迹')
    
    # 如果有协方差，则绘制置信椭圆
    if covariances is not None:
        for i in range(0, len(filtered_states), max(1, len(filtered_states) // 10)):
            confidence_ellipse(
                filt_x[i], filt_y[i], 
                covariances[i][0:2, 0:2],
                plt.gca(), n_std=2.0, edgecolor='blue', alpha=0.3
            )
    
    plt.legend()
    plt.xlabel('X')
    plt.ylabel('Y')
    
    if title:
        plt.title(title)
    else:
        plt.title('2D轨迹')
    
    plt.grid(True)
    plt.axis('equal')
    plt.tight_layout()
    
    return plt.gcf()


def confidence_ellipse(x, y, cov, ax, n_std=3.0, facecolor='none', **kwargs):
    """
    创建置信椭圆。
    
    参数:
        x, y: 椭圆中心
        cov: 协方差矩阵
        ax: matplotlib轴
        n_std: 标准差数量
        facecolor: 椭圆填充颜色
        **kwargs: 传递给Ellipse的其他参数
    """
    pearson = cov[0, 1] / np.sqrt(cov[0, 0] * cov[1, 1])
    
    # 使用特征值计算椭圆的宽度和高度
    eigvals, eigvecs = np.linalg.eigh(cov)
    
    # 标准差
    width, height = 2 * n_std * np.sqrt(eigvals)
    
    # 旋转角度
    angle = np.degrees(np.arctan2(eigvecs[1, 0], eigvecs[0, 0]))
    
    # 创建椭圆
    ellipse = Ellipse(
        xy=(x, y),
        width=width,
        height=height,
        angle=angle,
        facecolor=facecolor,
        **kwargs
    )
    
    return ax.add_patch(ellipse) 