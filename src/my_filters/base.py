"""
Base filter class that defines the common interface for all filters.
"""

import numpy as np
from abc import ABC, abstractmethod


class Filter(ABC):
    """
    抽象基类，定义所有滤波器的通用接口。
    
    所有滤波器实现都应继承此类并实现其抽象方法。
    """
    
    def __init__(self, dim_x, dim_z):
        """
        初始化滤波器。
        
        参数:
            dim_x (int): 状态向量的维度
            dim_z (int): 测量向量的维度
        """
        self.dim_x = dim_x
        self.dim_z = dim_z
        
        # 状态估计向量
        self.x = np.zeros((dim_x, 1))
        
        # 状态协方差矩阵
        self.P = np.eye(dim_x)
        
        # 过程噪声协方差矩阵
        self.Q = np.eye(dim_x)
        
        # 测量噪声协方差矩阵
        self.R = np.eye(dim_z)
        
        # 状态转移矩阵
        self.F = np.eye(dim_x)
        
        # 测量矩阵
        self.H = np.zeros((dim_z, dim_x))
        
        # 卡尔曼增益
        self.K = np.zeros((dim_x, dim_z))
        
        # 滤波器初始化标志
        self.initialized = False
    
    @abstractmethod
    def predict(self):
        """
        预测步骤: 根据系统模型预测下一状态。
        
        所有子类必须实现此方法。
        """
        pass
    
    @abstractmethod
    def update(self, z):
        """
        更新步骤: 根据测量值更新状态估计。
        
        参数:
            z: 测量值，可以是标量或向量
            
        所有子类必须实现此方法。
        """
        pass
    
    def reset(self):
        """
        重置滤波器状态。
        """
        self.x = np.zeros((self.dim_x, 1))
        self.P = np.eye(self.dim_x)
        self.initialized = False
    
    def get_state(self):
        """
        获取当前状态估计。
        
        返回:
            当前状态估计向量
        """
        return self.x
    
    def get_covariance(self):
        """
        获取当前状态协方差。
        
        返回:
            当前状态协方差矩阵
        """
        return self.P
    
    def run(self, measurements):
        """
        对一系列测量值运行滤波器。
        
        参数:
            measurements: 测量值列表或数组
            
        返回:
            filtered_states: 滤波后的状态估计列表
        """
        filtered_states = []
        
        for z in measurements:
            self.predict()
            self.update(z)
            filtered_states.append(self.x.copy())
            
        return filtered_states
