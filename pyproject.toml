[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "my_filters"
version = "0.1.0"
description = "A Python library for various filtering algorithms"
readme = "README.md"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
license = {text = "MIT"}
requires-python = ">=3.7"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Topic :: Scientific/Engineering",
]
dependencies = [
    "numpy>=1.19.0",
    "scipy>=1.5.0",
    "matplotlib>=3.3.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0.0",
    "pytest-cov>=2.10.0",
    "black>=22.1.0",
    "isort>=5.0.0",
]

[project.urls]
"Homepage" = "https://github.com/yourusername/my_filters"
"Bug Tracker" = "https://github.com/yourusername/my_filters/issues"

[tool.setuptools]
package-dir = {"" = "src"}

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
