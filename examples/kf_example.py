"""
卡尔曼滤波器使用示例，模拟一个匀速运动目标的跟踪。
"""

import numpy as np
import matplotlib.pyplot as plt
from src.my_filters import KalmanFilter, plot_filter_results

# 创建真实轨迹
def generate_trajectory(time_steps, dt=1.0):
    """生成匀速运动轨迹（带有少量过程噪声）"""
    # 初始状态 [位置, 速度]
    true_state = np.array([0., 1.])
    true_states = []
    
    # 状态转移矩阵
    F = np.array([[1., dt], [0., 1.]])
    
    # 过程噪声标准差
    process_noise_std = 0.1
    
    for _ in range(time_steps):
        # 保存当前状态
        true_states.append(true_state.copy())
        
        # 状态转移（加入少量过程噪声）
        true_state = F @ true_state + np.random.normal(0, process_noise_std, 2)
    
    return true_states

# 生成测量值
def generate_measurements(true_states, noise_std=0.5):
    """从真实轨迹生成带噪声的测量值"""
    # 测量矩阵 - 只测量位置
    H = np.array([[1., 0.]])
    
    measurements = []
    for state in true_states:
        # 生成带噪声的测量值
        z = H @ state + np.random.normal(0, noise_std)
        measurements.append(z)
    
    return measurements

def main():
    # 参数设置
    time_steps = 50    # 时间步长
    dt = 0.1           # 采样间隔
    noise_std = 0.5    # 测量噪声标准差
    
    # 生成真实轨迹
    true_states = generate_trajectory(time_steps, dt)
    
    # 生成测量值
    measurements = generate_measurements(true_states, noise_std)
    
    # 创建卡尔曼滤波器
    kf = KalmanFilter(dim_x=2, dim_z=1)
    
    # 设置初始状态
    kf.x = np.array([[0.], [0.]])  # 初始估计：位置=0，速度=0
    
    # 设置状态转移矩阵（匀速运动模型）
    kf.F = np.array([[1., dt], [0., 1.]])
    
    # 设置测量矩阵（只测量位置）
    kf.H = np.array([[1., 0.]])
    
    # 设置过程噪声协方差
    q = 0.01  # 过程噪声参数
    kf.Q = np.array([[dt**3/3, dt**2/2], 
                     [dt**2/2, dt]]) * q
    
    # 设置测量噪声协方差
    kf.R = np.array([[noise_std**2]])
    
    # 运行滤波器
    filtered_states = []
    for z in measurements:
        kf.predict()
        kf.update(z)
        filtered_states.append(kf.x.copy())
    
    # 提取真实位置和估计位置用于绘图
    true_positions = [state[0] for state in true_states]
    times = np.arange(0, time_steps * dt, dt)
    
    # 绘制结果
    plt.figure(figsize=(12, 6))
    
    # 使用内置的绘图函数
    plot_filter_results(
        times=times,
        measurements=measurements,
        filtered_states=filtered_states,
        true_states=[[x] for x in true_positions],
        labels=["时间 (s)", "位置"],
        title="卡尔曼滤波器跟踪示例"
    )
    
    plt.show()

if __name__ == "__main__":
    main() 